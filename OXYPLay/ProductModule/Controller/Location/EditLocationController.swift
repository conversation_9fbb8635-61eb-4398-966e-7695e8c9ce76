//
//  EditLocationController.swift
//  OXYPLay
//
//  Created by Renh<PERSON> on 2025/7/17.
//

import UIKit
import SnapKit
import Combine
import Then

class EditLocationController: BasePresentController {

    // MARK: - Properties
    let addressSavePublisher = PassthroughSubject<Bool, Never>()
    var dic = Dictionary<String, Any>()
    var isEditMode: Bool = false // 是否为编辑模式
    var addressType: Int = 1 // 地址类型：1=收货地址，2=退货地址

    // ViewModel for network requests
    private let viewModel = LocationSelectViewModel()

    private lazy var locationTitleLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        $0.textColor = UIColor(hexString: "#2B2C2F")
        $0.text = "地址信息"
    }

    private lazy var defualtButton = BaseButton().then {
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 11, weight: .regular)
        $0.setTitle("默认收货", for: .normal)
        $0.setTitleColor(UIColor(hexString: "#2B2C2F"), for: .normal)
        $0.setImage(UIImage(named: "baselist_single_normal"), for: .normal)
        $0.setImage(UIImage(named: "baselist_single_select"), for: .selected)
    }

    lazy var listView = BaseListView().then{
        $0.delegate = self
    }
    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        configLayout()
        configureListItems()
    }

    // MARK: - UI Configuration

    override func configUI() {
        // 根据地址类型设置标题
        let titlePrefix = addressType == 2 ? "退货地址" : "收货地址"
        let title = isEditMode ? "编辑\(titlePrefix)" : "新增\(titlePrefix)"
        let bottomTitle = "确认"
        configView(title: title, bottomTitle: bottomTitle)
        fd_prefersNavigationBarHidden = true
        // 添加子视图
        contentView.addSubview(locationTitleLabel)
        contentView.addSubview(defualtButton)
        contentView.addSubview(listView)

        // 根据地址类型设置默认按钮文字
        defualtButton.setTitle(addressType == 2 ? "默认退货" : "默认收货", for: .normal)
    }

    override func configLayout() {
        locationTitleLabel.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.top.equalTo(0)
        }

        defualtButton.snp.makeConstraints { make in
            make.right.equalTo(-12)
            make.centerY.equalTo(locationTitleLabel)
        }

        listView.snp.makeConstraints { make in
            make.top.equalTo(locationTitleLabel.snp.bottom).offset(12)
            make.left.right.equalToSuperview()
            make.height.equalTo(44*4)
            make.bottom.equalToSuperview()
        }

    }

    // 设置双向绑定
    override func setupBindings() {
        super.setupBindings()
        // 检查多种可能的默认地址标识
        if let isDefault = self.dic["isDefualt"] as? Bool {
            self.defualtButton.isSelected = isDefault
        } else if let isDefault = self.dic["is_default"] as? Bool {
            self.defualtButton.isSelected = isDefault
        } else {
            // 默认为false
            self.defualtButton.isSelected = false
        }

        // 配置确认按钮事件
        bottomButton.tapPublisher
            .sink { [weak self] _ in
                self?.saveAddress()
            }
            .store(in: &cancellables)

        // 配置默认按钮事件
        defualtButton.tapPublisher
            .sink { [weak self] _ in
                self?.defualtButton.isSelected.toggle()
            }
            .store(in: &cancellables)
    }
    private func configureListItems() {
        // 根据地址类型设置占位符文字
        let addressPlaceholder = addressType == 2 ? "请选择您的退货地址" : "请选择您的收货地址"
        let phonePlaceholder = addressType == 2 ? "请输入退货联系手机号" : "请输入收货手机号"

        let items: [[ListItemConfig]] = [
            [
                ListItemConfig(type: .titleInput, identifier: "联系人", data: self.dic["联系人"], isRequired: true, placeholder: "请输入联系人姓名", title: "联系人"),
                ListItemConfig(type: .titleInput, identifier: "手机号", data: self.dic["手机号"], isRequired: true, placeholder: phonePlaceholder, title: "手机号"),
                ListItemConfig(type: .starselect, identifier: "所在地区", data: self.dic["所在地区"], isRequired: true, placeholder: addressPlaceholder, title: "所在地区"),
                ListItemConfig(type: .titleInput, identifier: "详细地址", data: self.dic["详细地址"], isRequired: true, placeholder: "请输入您的详细地址", title: "详细地址")
            ]
        ]
        // 设置列表项
        listView.setItems(items)
        listView.scrollView.isScrollEnabled = false
    }

    // MARK: - Public Methods

    /// 更新地区信息
    func updateLocationData(_ locationData: [String: Any]) {
        // 更新dic中的地区信息
        for (key, value) in locationData {
            dic[key] = value
        }

        // 重新配置列表项以更新UI
        configureListItems()
    }

    // MARK: - Private Methods

    private func saveAddress() {
        // 获取表单数据
        let formData = listView.getAllData()

        // 验证必填字段
        guard let recipientName = formData["联系人"] as? String, !recipientName.isEmpty,
              let phone = formData["手机号"] as? String, !phone.isEmpty,
              let region = formData["所在地区"] as? String, !region.isEmpty else {
            // 显示错误提示
            showErrorAlert("请填写完整的地址信息")
            return
        }

        // 验证地区相关的必传参数
        guard let province = dic["province"] as? String, !province.isEmpty,
              let provinceCode = dic["province_code"] as? String, !provinceCode.isEmpty,
              let city = dic["city"] as? String, !city.isEmpty,
              let cityCode = dic["city_code"] as? String, !cityCode.isEmpty,
              let district = dic["district"] as? String, !district.isEmpty,
              let districtCode = dic["district_code"] as? String, !districtCode.isEmpty,
              let street = dic["street"] as? String, !street.isEmpty,
              let streetCode = dic["street_code"] as? String, !streetCode.isEmpty else {
            // 显示错误提示
            showErrorAlert("请选择完整的地区信息")
            return
        }

        // 构建地址数据 - 包含所有必传参数
        var addressData: [String: Any] = [
            // 基本信息
            "recipient_name": recipientName,
            "phone": phone,

            // 地区信息（必传）
            "province": province,
            "province_code": provinceCode,
            "city": city,
            "city_code": cityCode,
            "district": district,
            "district_code": districtCode,
            "street": street,
            "street_code": streetCode,

            // 其他信息
            "set_default": defualtButton.isSelected,

            // 地址类型
            "type": addressType
        ]

        // 添加详细地址（可选）
        if let detail = formData["详细地址"] as? String, !detail.isEmpty {
            addressData["detail"] = detail
        }

        // 如果是编辑模式，添加必传的address_id
        if isEditMode {
            guard let addressId = dic["address_id"] else {
                showErrorAlert("编辑模式缺少地址ID")
                return
            }
            addressData["address_id"] = addressId
        }

        // 调用API创建或更新地址
        if isEditMode {
            updateAddress(addressData: addressData)
        } else {
            createAddress(addressData: addressData)
        }
    }

    /// 创建新地址
    private func createAddress(addressData: [String: Any]) {
        viewModel.createAddress(addressData: addressData) { [weak self] success in
            if success {
                self?.showSuccessAlert("地址创建成功") {
                    // 通知上级刷新地址列表
                    self?.addressSavePublisher.send(true)
                    // 关闭页面
                    self?.dismiss(animated: true)
                }
            } else {
                self?.showErrorAlert("创建地址失败")
            }
        }
    }

    /// 更新地址
    private func updateAddress(addressData: [String: Any]) {
        viewModel.updateAddress(addressData: addressData) { [weak self] success in
            if success {
                self?.showSuccessAlert("地址更新成功") {
                    // 通知上级刷新地址列表
                    self?.addressSavePublisher.send(true)
                    // 关闭页面
                    self?.dismiss(animated: true)
                }
            } else {
                self?.showErrorAlert("更新地址失败")
            }
        }
    }
}

// MARK: - BaseListViewDelegate

extension EditLocationController: BaseListViewDelegate {
    func listViewUpdate(_ listView: BaseListView, with data: Any?) {

    }

    func listViewClick(_ listView: BaseListView, config: ListItemConfig) {
        if config.identifier == "所在地区" {
            // 保存当前表单数据
            dic = listView.getAllData()
            dic["isDefualt"] = defualtButton.isSelected

            // 直接push到地区选择页面
            let selectLocationController = ProductSelectLocationController()

            // 监听地区选择结果
            selectLocationController.locationSelectedPublisher
                .receive(on: DispatchQueue.main)
                .sink { [weak self] (province, city, district, street) in
                    self?.handleLocationSelected(province: province, city: city, district: district, street: street)
                }
                .store(in: &cancellables)

            navigationController?.pushViewController(selectLocationController, animated: true)
        }
    }

    func listViewValidate(_ listView: BaseListView, message: String) {

    }

    // MARK: - Location Selection Handler

    /// 处理地区选择结果
    private func handleLocationSelected(province: RegionListItemModel, city: RegionListItemModel?, district: RegionListItemModel?, street: RegionListItemModel?) {
        // 构建地区显示文本
        var regionText = province.name
        if let city = city {
            regionText += " " + city.name
        }
        if let district = district {
            regionText += " " + district.name
        }
        if let street = street {
            regionText += " " + street.name
        }

        // 更新地区相关数据
        dic["所在地区"] = regionText
        dic["province"] = province.name
        dic["province_code"] = province.code

        if let city = city {
            dic["city"] = city.name
            dic["city_code"] = city.code
        }

        if let district = district {
            dic["district"] = district.name
            dic["district_code"] = district.code
        }

        if let street = street {
            dic["street"] = street.name
            dic["street_code"] = street.code
        }

        // 重新配置列表项以更新UI
        configureListItems()
    }
}


