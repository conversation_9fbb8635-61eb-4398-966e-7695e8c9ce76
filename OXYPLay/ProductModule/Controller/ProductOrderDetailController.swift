//
//  ProductOrderDetailController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/25.
//

import UIKit
import Combine
import CombineCocoa

/// 订单详情控制器
class ProductOrderDetailController: BaseViewController {
    
    // MARK: - Properties
    /// 订单ID
    var orderId: Int = 0

    /// ViewModel
    private var viewModel: OrderDetailViewModel!
    
    let tempViewModel = LocationSelectViewModel()
    // MARK: - UI Components
    
    /// 主滚动视图
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsVerticalScrollIndicator = false
        return scrollView
    }()
    
    /// 内容容器视图
    private lazy var contentView: UIStackView = {
        let view = UIStackView()
        view.axis = .vertical
        view.spacing = 12
        return view
    }()
    
    /// 订单状态视图
    private lazy var statusView: OrderStatusView = {
        let view = OrderStatusView()
        return view
    }()
    
    /// 收货地址视图
    private lazy var addressView: OrderAddressView = {
        let view = OrderAddressView()
        return view
    }()
  
    /// 订单信息视图
    private lazy var orderInfoView: OrderInfoView = {
        let view = OrderInfoView()
        return view
    }()
    
    /// 底部操作视图
    private lazy var bottomActionView: BaseTabToolBar = {
        let view = BaseTabToolBar()
        view.delegate = self
        return view
    }()
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()

        // 初始化ViewModel
        viewModel = OrderDetailViewModel(orderId: orderId)

        // 设置临时ViewModel的地址类型为退货地址
        tempViewModel.addressType = .refund

        setupUI()
        bindViewModel()
        loadData()
    }
    
    // MARK: - Setup Methods
    
    private func setupUI() {
        title = "订单详情"
        // 添加子视图
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        
        contentView.addArrangedSubview(statusView)
        contentView.addArrangedSubview(addressView)
        contentView.addArrangedSubview(orderInfoView)
        
        view.addSubview(bottomActionView)
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide)
            make.left.right.equalToSuperview()
            make.bottom.equalTo(bottomActionView.snp.top)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(scrollView)
        }
        
    
        bottomActionView.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(80)
        }
    }
    
    private func bindViewModel() {
        

        // 绑定请求状态
        viewModel.$requestState
            .receive(on: DispatchQueue.main)
            .sink { [weak self] state in
                switch state {
                case .failure(let message):
                    self?.showError(message: message)
                default:
                    break
                }
            }
            .store(in: &cancellables)
        
        // 绑定订单详情数据
        viewModel.$orderDetail
            .receive(on: DispatchQueue.main)
            .sink { [weak self] orderDetail in
                if let orderDetail = orderDetail {
                    self?.updateUI(with: orderDetail)
                }
            }
            .store(in: &cancellables)

        // 绑定申请退款成功消息
        viewModel.$refundSuccessMessage
            .receive(on: DispatchQueue.main)
            .sink { [weak self] message in
                if let message = message {
                    self?.showSuccessMessage(message)
                    // 重置消息
                    self?.viewModel.refundSuccessMessage = nil
                }
            }
            .store(in: &cancellables)

        // 绑定申请退款失败消息
        viewModel.$refundErrorMessage
            .receive(on: DispatchQueue.main)
            .sink { [weak self] message in
                if let message = message {
                    self?.showError(message: message)
                    // 重置消息
                    self?.viewModel.refundErrorMessage = nil
                }
            }
            .store(in: &cancellables)
    }
    
    private func loadData() {
        guard orderId > 0 else {
            showError(message: "订单ID无效")
            return
        }
        viewModel.refreshData()
    }
    
    private func updateUI(with orderDetail: OrderDetailModel) {
        statusView.configure(orderStatus: orderDetail.status,
                           statusDetailText: orderDetail.status_detail_text,
                           statusDeadline: orderDetail.status_deadline,
                             statusMap: orderDetail.status_map)

        addressView.configure(with: orderDetail)
        orderInfoView.configure(with: orderDetail)

        // 配置底部工具栏
        configureBottomToolBar(with: orderDetail.action_buttons)
    }

    /// 配置底部工具栏
    /// - Parameter actionButtons: 操作按钮数组
    private func configureBottomToolBar(with actionButtons: [ActionButton]) {
        // 左侧固定按钮：联系卖家
        let leftItem = ToolBarButtonItem(
            normalImage: UIImage(named: "prodcut_order_chat"),
            selectedImage: UIImage(named: "prodcut_order_chat"),
            title: "联系卖家"
        )

        // 右侧按钮：根据接口数据动态设置
        let rightItems = actionButtons.enumerated().map { index, actionButton in
            ToolBarButtonItem(
                normalImage: nil,
                selectedImage: nil,
                title: actionButton.title,
                isSelected: false,
                tag: index,
                titleColor: UIColor(hexString: actionButton.title_color) ?? UIColor(hexString: "2A72FF")!,
                backgroundColor: UIColor(hexString: actionButton.bg_color) ?? UIColor.clear,
                borderColor: UIColor(hexString: actionButton.title_color) ?? UIColor(hexString: "2A72FF")!
            )
        }

        // 使用左侧一个固定按钮，右侧多个固定宽度按钮的样式
        bottomActionView.configureLeftSingleRightMultiple(leftItem: leftItem, rightItems: rightItems)
    }
    
    
    private func showError(message: String) {
        // TODO: 显示错误提示
        print("错误: \(message)")
    }

    private func showSuccessMessage(_ message: String) {
        // TODO: 显示成功提示
        print("成功: \(message)")
    }

}

// MARK: - TabToolBarDelegate

extension ProductOrderDetailController: TabToolBarDelegate {
    func tabToolBar(_ toolBar: BaseTabToolBar, didClickLeftButtonAt index: Int, item: ToolBarButtonItem) {
        // 左侧按钮点击 - 联系卖家
        if item.title == "联系卖家" {
            print("联系卖家按钮被点击")
        }
    }

    func tabToolBar(_ toolBar: BaseTabToolBar, didClickRightButtonAt index: Int, item: ToolBarButtonItem?) {
        // 右侧按钮点击 - 根据接口数据处理
        guard let orderDetail = viewModel.orderDetail,
              index < orderDetail.action_buttons.count else { return }

        let actionButton = orderDetail.action_buttons[index]
        switch actionButton.action {
        case OrderEventAction.cancel.rawValue,OrderEventAction.cancelOrder.rawValue:
            cancelOrder()
        case OrderEventAction.review.rawValue:
            createComment()
        case OrderEventAction.viewReview.rawValue:
            lookComment()
        case OrderEventAction.viewLogistics.rawValue:
            ​getTrackingInfo()
        case OrderEventAction.pay.rawValue:
            showPayment()
        case OrderEventAction.applyRefund.rawValue:
            applyRefund()
        case OrderEventAction.ship.rawValue:
            proceedtoShipping()
        case OrderEventAction.agreeRefund.rawValue:
            agreeRefund()
        default:
            print("")
        }
    }
    ///同意退款
    func agreeRefund(){
        guard let orderDetail = viewModel.orderDetail else { return }
         //判断退款类型
        if orderDetail.refund_type == 1 {
            // 仅退款，弹窗确认
            showRefundOnlyConfirmDialog()
        } else {
            // 退货退款，需要选择退货地址
            handleRefundWithReturn()
        }
    }
    ///选择发货方式
    func proceedtoShipping(){
        
    }
    ///查看快递
    func ​getTrackingInfo(){
        guard let logistics = viewModel.orderDetail?.logistics else { return }
        let vc = ProductTrackingInfoController()
        vc.orderLogistics = logistics
        customPresent(vc, animated: true)
    }
    /// 显示支付方式选择弹窗
    func showPayment() {
        guard let orderDetail = viewModel.orderDetail else { return }
        let combinedPaymentController = CombinedPaymentController()
        // 设置订单总金额（到手价）
        combinedPaymentController.setTotalAmount(orderDetail.paid_amount)
        combinedPaymentController.orderId = orderDetail.order_id
        customPresent(combinedPaymentController, animated: true)
    }

    ///取消订单
    func cancelOrder(){
        viewModel.cancelOrder()
    }
    ///查看评价
    func lookComment(){
        
    }
    ///创建评价
    func createComment(){
        guard let items = viewModel.orderDetail?.items else{
            return
        }
        let vc = ProductOrderRetreatController()
        vc.items = items
        vc.isRetreat = false
        customPresent(vc, animated: true)
        present(vc, animated: true, completion: nil)
        vc.selectCompletePublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] selectedItems in
                guard let self = self,let firstItem = selectedItems.first(where: {$0.isSelect == true}) else { return }

                let commentVc = ProductCreateCommentController()
                commentVc.productId = firstItem.product_id
                commentVc.specValueText = firstItem.spec_value_text

                // 监听评价完成事件
                commentVc.commentCompletePublisher
                    .receive(on: DispatchQueue.main)
                    .sink { [weak self] response in
                        self?.viewModel.refreshData()
                    }
                    .store(in: &self.cancellables)

                self.customPresent(commentVc, animated: true)
            }
            .store(in: &cancellables)
    }
    /// 申请退款
    func applyRefund() {
        guard let items = viewModel.orderDetail?.items else {
            return
        }

        // 第一步：选择退款商品
        let vc = ProductOrderRetreatController()
        vc.items = items
        vc.isRetreat = true // 设置为退款模式
        customPresent(vc, animated: true)

        vc.selectCompletePublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] selectedItems in
                guard let self = self else { return }

                // 获取选中的商品
                let selectedOrderItems = selectedItems.filter { $0.isSelect }
                guard !selectedOrderItems.isEmpty else {
                    print("未选择任何商品")
                    return
                }

                // 第二步：选择退款原因
                let reasonController = ProductOrderRetreatReasonController()
                self.customPresent(reasonController, animated: true)

                reasonController.selectCompletePublisher
                    .receive(on: DispatchQueue.main)
                    .sink { [weak self] reasonData in
                        guard let self = self else { return }
                        // 获取选中商品的ID
                        let orderItemIds = selectedOrderItems.map { $0.id }
                        let orderItemIdsString = orderItemIds.map { String($0) }.joined(separator: ",")
                        var param = reasonData
                        param["order_id"] = self.viewModel.orderDetail?.order_id
                        param["order_item_ids"] = orderItemIdsString
                        // 调用申请退款接口
                        self.viewModel.applyRefund(params: param)
                    }
                    .store(in: &self.cancellables)
            }
            .store(in: &cancellables)
    }
}
///卖家同意退款按钮点击相关方法
extension ProductOrderDetailController{
      /// 显示仅退款确认弹窗
      private func showRefundOnlyConfirmDialog() {
          let alertDialog = BaseAlertDialog()
          alertDialog.configure(
              title: "同意后退款将直接打给买家，是否确定同意退款？",
              leftButtonTitle: "确定",
              rightButtonTitle: "取消",
              showCloseButton: true,
              leftAction: { [weak self] in
                  // 用户点击确定，调用同意退款接口
                  self?.viewModel.agreeRefund()
              },
              rightAction: {
                  // 用户点击取消，什么都不做
              }
          )
          alertDialog.show(in: self)
      }

      /// 处理退货退款
      private func handleRefundWithReturn() {
          // 先检查是否有退货地址
          checkRefundAddressAndProceed()
      }

      /// 检查退货地址并继续处理
      private func checkRefundAddressAndProceed() {
    
          tempViewModel.fetchRefundAddressList()

          // 监听地址数据
          tempViewModel.$productAddressModel
              .receive(on: DispatchQueue.main)
              .sink { [weak self] addressModel in
                  guard let self = self, let addressModel = addressModel else { return }

                  if addressModel.address_list.isEmpty && addressModel.default_address.id.isEmpty {
                      // 没有退货地址，直接弹出编辑退货地址控制器
                      self.showEditRefundAddressController()
                  } else {
                      // 有退货地址，弹出选择退货地址控制器
                      self.showSelectRefundAddressController()
                  }
              }
              .store(in: &cancellables)
      }

      /// 显示选择退货地址控制器
      private func showSelectRefundAddressController() {
          let selectController = SelectRefundAddressController()

          selectController.addressSelectedPublisher
              .receive(on: DispatchQueue.main)
              .sink { [weak self] selectedAddress in
                  self?.agreeRefundWithAddress(selectedAddress)
              }
              .store(in: &cancellables)

          customPresent(selectController, animated: true)
      }

      /// 显示编辑退货地址控制器
      private func showEditRefundAddressController() {
          let editController = EditLocationController()
          editController.dic = [:]
          editController.isEditMode = false
          editController.addressType = 2 // 设置为退货地址类型

          editController.addressSavePublisher
              .receive(on: DispatchQueue.main)
              .sink { [weak self] success in
                  if success {
                      // 地址创建成功，继续处理退货申请
                      self?.handleRefundAddressCreated()
                  }
              }
              .store(in: &cancellables)

          // 使用导航控制器包装
          let navController = BaseNavigationController(rootViewController: editController)
          customPresent(navController, animated: true)
      }

      /// 同意退款并发送地址
      private func agreeRefundWithAddress(_ address: ProductAddressItemModel) {
          // 构建地址快照
          let addressSnapshot: [String: Any] = [
              "name": address.recipient_name,
              "phone": address.phone,
              "detail": "\(address.province)\(address.city)\(address.district)\(address.street) \(address.detail)"
          ]

          // 转换为JSON字符串
          if let jsonData = try? JSONSerialization.data(withJSONObject: addressSnapshot),
             let jsonString = String(data: jsonData, encoding: .utf8) {

              // 调用同意退款接口
              viewModel.agreeRefund(
                  addressId: Int(address.id),
                  addressSnapshot: jsonString
              )
          }
      }

      /// 处理退货地址创建成功
      private func handleRefundAddressCreated() {
          // 重新获取退货地址列表，然后弹出选择退货地址控制器
          tempViewModel.fetchRefundAddressList()

          // 监听地址数据更新
          tempViewModel.$productAddressModel
              .receive(on: DispatchQueue.main)
              .sink { [weak self] addressModel in
                  guard let self = self, let addressModel = addressModel else { return }

                  if !addressModel.address_list.isEmpty || !addressModel.default_address.id.isEmpty {
                      // 有退货地址了，弹出选择退货地址控制器
                      self.showSelectRefundAddressController()
                  }
              }
              .store(in: &cancellables)
      }
}
