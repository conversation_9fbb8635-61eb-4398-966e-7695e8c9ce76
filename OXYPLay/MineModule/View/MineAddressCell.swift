//
//  MineAddressCell.swift
//  OXYPLay
//
//  Created by Renhw on 2025/8/4.
//

import UIKit
import SnapKit
import Combine
import Then

/// 地址列表Cell
class MineAddressCell: UITableViewCell {

    // MARK: - Properties
    
    var editButtonTapped: ((ProductAddressItemModel) -> Void)?
    var deleteButtonTapped: ((ProductAddressItemModel) -> Void)?
    var defaultButtonTapped: ((ProductAddressItemModel) -> Void)?
    
    private var address: ProductAddressItemModel?
    private var cancellables = Set<AnyCancellable>()

    // MARK: - UI Components
    
    private lazy var containerView = UIView().then {
        $0.backgroundColor = .white
        $0.layer.cornerRadius = 16
        $0.masksToBounds = true
    }
    
    private lazy var defaultLabel = UILabel().then {
        $0.text = "默认"
        $0.font = UIFont.systemFont(ofSize: 12)
        $0.backgroundColor = UIColor(hexString: "#FF0000", transparency: 0.08)
        $0.textColor = UIColor(hexString: "#FF0000")
        $0.textAlignment = .center
        $0.layer.cornerRadius = 16
        $0.layer.masksToBounds = true
        $0.layer.maskedCorners = [.layerMinXMaxYCorner, .layerMaxXMinYCorner]
        $0.isHidden = true
    }
    
    private lazy var namePhoneLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        $0.textColor = color_2B2C2F
    }
    
    private lazy var editButton = UIButton(type: .custom).then {
        $0.setImage(UIImage(systemName: "pencil"), for: .normal)
        $0.tintColor = color_999DA1
    }
    
    private lazy var addressLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 12)
        $0.textColor = UIColor(hexString: "#2B2C2F", transparency: 0.56)
        $0.numberOfLines = 0
    }
    
    private lazy var detailLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 12)
        $0.textColor = UIColor(hexString: "#2B2C2F", transparency: 1)
        $0.numberOfLines = 0
    }
    
    // 管理模式下的控件
    private lazy var defaultButton = UIButton(type: .custom).then {
        $0.setImage(UIImage(named: "baselist_single_normal"), for: .normal)
        $0.setImage(UIImage(named: "baselist_single_select"), for: .selected)
        $0.setTitle("默认收货地址", for: .normal)
        $0.setTitleColor(color_2B2C2F, for: .normal)
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 12)
        $0.imageEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 8)
        $0.isHidden = true
    }
    
    private lazy var deleteButton = UIButton(type: .custom).then {
        $0.setImage(UIImage(systemName: "trash"), for: .normal)
        $0.setTitle("删除", for: .normal)
        $0.setTitleColor(color_999DA1, for: .normal)
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 12)
        $0.tintColor = color_999DA1
        $0.imageEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 4)
        $0.isHidden = true
    }

    // MARK: - Initialization
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
        setupBindings()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Setup
    
    private func setupUI() {
        backgroundColor = color_F6F8F9
        selectionStyle = .none
        
        contentView.addSubview(containerView)
        containerView.addSubview(defaultLabel)
        containerView.addSubview(namePhoneLabel)
        containerView.addSubview(editButton)
        containerView.addSubview(addressLabel)
        containerView.addSubview(detailLabel)
        containerView.addSubview(defaultButton)
        containerView.addSubview(deleteButton)
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(8)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.bottom.equalToSuperview().offset(-8)
        }
        
        defaultLabel.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.right.equalToSuperview()
            make.width.equalTo(42)
            make.height.equalTo(19)
        }
        
        namePhoneLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.left.equalToSuperview().offset(16)
            make.right.lessThanOrEqualTo(editButton.snp.left).offset(-8)
        }
        
        editButton.snp.makeConstraints { make in
            make.centerY.equalTo(namePhoneLabel)
            make.right.equalToSuperview().offset(-16)
            make.width.height.equalTo(20)
        }
        
        addressLabel.snp.makeConstraints { make in
            make.top.equalTo(namePhoneLabel.snp.bottom).offset(8)
            make.left.equalTo(namePhoneLabel)
            make.right.equalTo(editButton.snp.left).offset(-8)
        }
        
        detailLabel.snp.makeConstraints { make in
            make.top.equalTo(addressLabel.snp.bottom).offset(4)
            make.left.equalTo(namePhoneLabel)
            make.right.equalTo(editButton.snp.left).offset(-8)
            make.bottom.lessThanOrEqualTo(defaultButton.snp.top).offset(-8)

        }
        
        defaultButton.snp.makeConstraints { make in
            make.top.equalTo(detailLabel.snp.bottom).offset(8)
            make.left.equalTo(namePhoneLabel)
            make.bottom.lessThanOrEqualToSuperview().offset(-12)
        }
        
        deleteButton.snp.makeConstraints { make in
            make.centerY.equalTo(defaultButton)
            make.right.equalToSuperview().offset(-16)
        }
    }
    
    private func setupBindings() {
        editButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self, let address = self.address else { return }
                self.editButtonTapped?(address)
            }
            .store(in: &cancellables)
        
        deleteButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self, let address = self.address else { return }
                self.deleteButtonTapped?(address)
            }
            .store(in: &cancellables)
        
        defaultButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self, let address = self.address else { return }
                self.defaultButtonTapped?(address)
            }
            .store(in: &cancellables)
    }

    // MARK: - Configuration
    
    func configure(with address: ProductAddressItemModel, isManageMode: Bool) {
        self.address = address
        
        namePhoneLabel.text = "\(address.recipient_name) 86-\(address.phone.maskPhoneNumber)"
        addressLabel.text = address.region
        detailLabel.text = address.detail
        
        // 显示默认标签
        defaultLabel.isHidden = !address.is_default
        
        // 管理模式下的UI调整
        defaultButton.isHidden = !isManageMode
        deleteButton.isHidden = !isManageMode
        defaultButton.isSelected = address.is_default
        
        // 调整约束
//        if isManageMode {
//            detailLabel.snp.updateConstraints { make in
//                make.bottom.lessThanOrEqualTo(defaultButton.snp.top).offset(-8)
//            }
//        } else {
//            detailLabel.snp.updateConstraints { make in
//                make.bottom.lessThanOrEqualToSuperview().offset(-12)
//            }
//        }
    }
}
