//
//  MineAddressManagerController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/8/4.
//

import UIKit
import SnapKit
import Combine
import Then

/// 地址管理主控制器
class MineAddressManagerController: BaseViewController {

    // MARK: - UI Components

    private lazy var listView = BaseListView().then {
        $0.delegate = self
    }

    // MARK: - Properties

    private let menuItems = [
        AddressManagerItem(title: "收货地址", type: .shipping),
        AddressManagerItem(title: "退货地址", type: .refund)
    ]

    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        setupBindings()
        configureListItems()
    }

    // MARK: - UI Configuration

    override func configUI() {
        title = "地址管理"
        view.backgroundColor = color_F6F8F9

        view.addSubview(listView)

        listView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
    }

    override func setupBindings() {
        // 这里可以添加数据绑定逻辑
    }

    // MARK: - Private Methods

    private func configureListItems() {
        let configs = menuItems.map { item in
            ListItemConfig.select(
                identifier: item.type.apiType,
                title: item.title,
                iconString: item.type == .shipping ? "location" : "return_location",
                data: item
            )
        }

        listView.setItems([configs])
    }

    private func navigateToAddressList(type: AddressType) {
        let addressListController = MineAddressListController(addressType: type)
        pushVc(addressListController, animated: true)
    }
}

// MARK: - BaseListViewDelegate

extension MineAddressManagerController: BaseListViewDelegate {
    func listViewClick(_ listView: BaseListView, config: ListItemConfig) {
        guard let item = config.data as? AddressManagerItem else { return }
        navigateToAddressList(type: item.type)
    }
}

// MARK: - Models

enum AddressType: Int {
    case shipping = 1  // 收货地址
    case refund = 2    // 退货地址

    var title: String {
        switch self {
        case .shipping:
            return "收货地址"
        case .refund:
            return "退货地址"
        }
    }

    var apiType: String {
        switch self {
        case .shipping:
            return "1"
        case .refund:
            return "2"
        }
    }
}

struct AddressManagerItem {
    let title: String
    let type: AddressType
}

